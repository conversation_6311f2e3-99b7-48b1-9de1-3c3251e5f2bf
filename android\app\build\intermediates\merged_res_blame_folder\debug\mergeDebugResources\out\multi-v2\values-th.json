{"logs": [{"outputFile": "com.usefulteam.easy_social_share_example.app-mergeDebugResources-33:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\09538e6dbf47d754d340c6096b073b9b\\transformed\\jetified-facebook-common-17.0.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,203,332,485,602,688,774,851,940,1034,1151,1263,1357,1451,1559,1683,1762,1843,2032,2128,2234,2353,2463", "endColumns": "147,128,152,116,85,85,76,88,93,116,111,93,93,107,123,78,80,188,95,105,118,109,123", "endOffsets": "198,327,480,597,683,769,846,935,1029,1146,1258,1352,1446,1554,1678,1757,1838,2027,2123,2229,2348,2458,2582"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3449,3597,3726,3879,3996,4082,4168,4245,4334,4428,4545,4657,4751,4845,4953,5077,5156,5237,5426,5522,5628,5747,5857", "endColumns": "147,128,152,116,85,85,76,88,93,116,111,93,93,107,123,78,80,188,95,105,118,109,123", "endOffsets": "3592,3721,3874,3991,4077,4163,4240,4329,4423,4540,4652,4746,4840,4948,5072,5151,5232,5421,5517,5623,5742,5852,5976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f958ee96b464852d797ff4a06c0b43c\\transformed\\core-1.15.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "29,30,31,32,33,34,35,60", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2734,2830,2933,3031,3129,3232,3337,6062", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "2825,2928,3026,3124,3227,3332,3444,6158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,864,955,1047,1138,1232,1333,1426,1521,1615,1706,1797,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,77,90,91,90,93,100,92,94,93,90,90,79,102,97,97,102,105,100,152,94,80", "endOffsets": "205,298,406,491,593,703,781,859,950,1042,1133,1227,1328,1421,1516,1610,1701,1792,1872,1975,2073,2171,2274,2380,2481,2634,2729,2810"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,864,955,1047,1138,1232,1333,1426,1521,1615,1706,1797,1877,1980,2078,2176,2279,2385,2486,2639,5981", "endColumns": "104,92,107,84,101,109,77,77,90,91,90,93,100,92,94,93,90,90,79,102,97,97,102,105,100,152,94,80", "endOffsets": "205,298,406,491,593,703,781,859,950,1042,1133,1227,1328,1421,1516,1610,1701,1792,1872,1975,2073,2171,2274,2380,2481,2634,2729,6057"}}]}]}