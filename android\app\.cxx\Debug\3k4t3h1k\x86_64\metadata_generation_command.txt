                        -HC:\Users\<USER>\fvm\versions\stable\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON><PERSON><PERSON>OID_PLATFORM=android-23
-D<PERSON>DROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\29.0.13113456
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\29.0.13113456
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\29.0.13113456\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\flutter-packages\easy_social_share\example\android\app\build\intermediates\cxx\Debug\3k4t3h1k\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\flutter-packages\easy_social_share\example\android\app\build\intermediates\cxx\Debug\3k4t3h1k\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BD:\flutter-packages\easy_social_share\example\android\app\.cxx\Debug\3k4t3h1k\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2