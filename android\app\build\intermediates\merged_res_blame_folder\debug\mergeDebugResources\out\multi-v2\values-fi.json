{"logs": [{"outputFile": "com.usefulteam.easy_social_share_example.app-mergeDebugResources-33:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f958ee96b464852d797ff4a06c0b43c\\transformed\\core-1.15.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "29,30,31,32,33,34,35,60", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2775,2871,2973,3071,3176,3281,3393,6162", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "2866,2968,3066,3171,3276,3388,3504,6258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,897,988,1080,1175,1269,1364,1457,1553,1652,1743,1837,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,79,90,91,94,93,94,92,95,98,90,93,78,106,100,96,105,99,97,149,99,79", "endOffsets": "208,308,417,503,608,726,812,892,983,1075,1170,1264,1359,1452,1548,1647,1738,1832,1911,2018,2119,2216,2322,2422,2520,2670,2770,2850"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,897,988,1080,1175,1269,1364,1457,1553,1652,1743,1837,1916,2023,2124,2221,2327,2427,2525,2675,6082", "endColumns": "107,99,108,85,104,117,85,79,90,91,94,93,94,92,95,98,90,93,78,106,100,96,105,99,97,149,99,79", "endOffsets": "208,308,417,503,608,726,812,892,983,1075,1170,1264,1359,1452,1548,1647,1738,1832,1911,2018,2119,2216,2322,2422,2520,2670,2770,6157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\09538e6dbf47d754d340c6096b073b9b\\transformed\\jetified-facebook-common-17.0.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,348,494,606,690,777,853,943,1041,1159,1276,1373,1470,1583,1716,1798,1878,2052,2147,2260,2374,2492", "endColumns": "161,130,145,111,83,86,75,89,97,117,116,96,96,112,132,81,79,173,94,112,113,117,135", "endOffsets": "212,343,489,601,685,772,848,938,1036,1154,1271,1368,1465,1578,1711,1793,1873,2047,2142,2255,2369,2487,2623"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3509,3671,3802,3948,4060,4144,4231,4307,4397,4495,4613,4730,4827,4924,5037,5170,5252,5332,5506,5601,5714,5828,5946", "endColumns": "161,130,145,111,83,86,75,89,97,117,116,96,96,112,132,81,79,173,94,112,113,117,135", "endOffsets": "3666,3797,3943,4055,4139,4226,4302,4392,4490,4608,4725,4822,4919,5032,5165,5247,5327,5501,5596,5709,5823,5941,6077"}}]}]}