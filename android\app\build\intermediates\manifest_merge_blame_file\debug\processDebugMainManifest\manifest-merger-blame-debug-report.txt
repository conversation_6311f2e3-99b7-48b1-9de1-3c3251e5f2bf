1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.usefulteam.easy_social_share_example"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:93:5-67
15-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:93:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:52:5-57:15
24        <intent>
24-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:53:9-56:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:54:13-73
25-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:54:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:55:13-51
27-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:55:19-48
28        </intent>
29        <intent>
29-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.2.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
30            <action android:name="android.intent.action.GET_CONTENT" />
30-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.2.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
30-->[:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.2.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
31
32            <data android:mimeType="*/*" />
32-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:55:13-51
32-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:55:19-48
33        </intent>
34
35        <package android:name="com.facebook.katana" />
35-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:18:9-55
35-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:18:18-52
36    </queries>
37    <queries>
37-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:52:5-57:15
38
39        <!-- Explicit apps you know in advance about: -->
40
41        <package android:name="com.instagram.android" />
42        <package android:name="com.zhiliaoapp.musically" />
43        <package android:name="com.facebook.katana" />
43-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:18:9-55
43-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:18:18-52
44        <package android:name="com.facebook.orca" />
45        <package android:name="org.telegram.messenger" />
46        <package android:name="com.whatsapp" />
47        <package android:name="com.twitter.android" />
48
49        <!-- allows app to access Facebook app features -->
50        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
51
52        <!--
53        allows
54        sharing to Messenger app
55        -->
56        <provider android:authorities="com.facebook.orca.provider.PlatformProvider" />
57    </queries>
58    <!--
59 Required only if your app needs to access images or photos
60       that other apps created.
61    -->
62    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
62-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:79:5-76
62-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:79:22-73
63    <!--
64 Required only if your app needs to access videos
65       that other apps created.
66    -->
67    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
67-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:83:5-75
67-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:83:22-72
68    <!--
69 Required only if your app needs to access audio files
70       that other apps created.
71    -->
72    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
72-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:87:5-75
72-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:87:22-72
73    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
73-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:89:5-80
73-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:89:22-77
74    <uses-permission
74-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:91:5-92:38
75        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
75-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:91:22-78
76        android:maxSdkVersion="35" />
76-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:92:9-35
77    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
77-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:16:5-79
77-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:16:22-76
78
79    <permission
79-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
80        android:name="com.usefulteam.easy_social_share_example.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
80-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
81        android:protectionLevel="signature" />
81-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
82
83    <uses-permission android:name="com.usefulteam.easy_social_share_example.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
83-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
83-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
84    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
84-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\358dd9479c84a3ed01b3c101674f7c01\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
84-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\358dd9479c84a3ed01b3c101674f7c01\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
85
86    <application
87        android:name="android.app.Application"
88        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
88-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
89        android:debuggable="true"
90        android:extractNativeLibs="false"
91        android:icon="@mipmap/ic_launcher"
92        android:label="easy_social_share_example"
93        android:supportsRtl="true" >
93-->[com.facebook.android:facebook-share:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7feabc10bdb4aab53c74638db0e2916f\transformed\jetified-facebook-share-17.0.0\AndroidManifest.xml:16:18-44
94        <activity
95            android:name="com.usefulteam.easy_social_share_example.MainActivity"
96            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
97            android:exported="true"
98            android:hardwareAccelerated="true"
99            android:launchMode="singleTop"
100            android:taskAffinity=""
101            android:theme="@style/LaunchTheme"
102            android:windowSoftInputMode="adjustResize" >
103
104            <!--
105                 Specifies an Android theme to apply to this Activity as soon as
106                 the Android process has started. This theme is visible to the user
107                 while the Flutter UI initializes. After that, this theme continues
108                 to determine the Window background behind the Flutter UI.
109            -->
110            <meta-data
111                android:name="io.flutter.embedding.android.NormalTheme"
112                android:resource="@style/NormalTheme" />
113
114            <intent-filter>
115                <action android:name="android.intent.action.MAIN" />
116
117                <category android:name="android.intent.category.LAUNCHER" />
118            </intent-filter>
119        </activity>
120        <!--
121             Don't delete the meta-data below.
122             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
123        -->
124        <meta-data
125            android:name="flutterEmbedding"
126            android:value="2" />
127
128        <!-- FileProvider for sharing files -->
129        <provider
130            android:name="androidx.core.content.FileProvider"
131            android:authorities="com.usefulteam.easy_social_share_example.provider"
132            android:exported="false"
133            android:grantUriPermissions="true" >
134            <meta-data
135                android:name="android.support.FILE_PROVIDER_PATHS"
136                android:resource="@xml/provider_paths" />
137        </provider>
138
139        <activity
139-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:22:9-25:66
140            android:name="com.facebook.FacebookActivity"
140-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:23:13-57
141            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
141-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:24:13-96
142            android:theme="@style/com_facebook_activity_theme" />
142-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:25:13-63
143        <activity android:name="com.facebook.CustomTabMainActivity" />
143-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:26:9-71
143-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:26:19-68
144        <activity
144-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:27:9-41:20
145            android:name="com.facebook.CustomTabActivity"
145-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:28:13-58
146            android:exported="true" >
146-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:29:13-36
147            <intent-filter>
147-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:31:13-40:29
148                <action android:name="android.intent.action.VIEW" />
148-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:32:17-69
148-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:32:25-66
149
150                <category android:name="android.intent.category.DEFAULT" />
150-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:34:17-76
150-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:34:27-73
151                <category android:name="android.intent.category.BROWSABLE" />
151-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:35:17-78
151-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:35:27-75
152
153                <data
153-->D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:55:13-51
154                    android:host="cct.com.usefulteam.easy_social_share_example"
155                    android:scheme="fbconnect" />
156            </intent-filter>
157        </activity>
158        <!--
159         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
160         with the application context. This config is merged in with the host app's manifest,
161         but there can only be one provider with the same authority activated at any given
162         point; so if the end user has two or more different apps that use Facebook SDK, only the
163         first one will be able to use the provider. To work around this problem, we use the
164         following placeholder in the authority to identify each host application as if it was
165         a completely different provider.
166        -->
167        <provider
167-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:29:9-32:40
168            android:name="com.facebook.internal.FacebookInitProvider"
168-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:30:13-70
169            android:authorities="com.usefulteam.easy_social_share_example.FacebookInitProvider"
169-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:31:13-72
170            android:exported="false" />
170-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:32:13-37
171
172        <receiver
172-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:34:9-40:20
173            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
173-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:35:13-86
174            android:exported="false" >
174-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:36:13-37
175            <intent-filter>
175-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:37:13-39:29
176                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
176-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:38:17-95
176-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:38:25-92
177            </intent-filter>
178        </receiver>
179        <receiver
179-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:41:9-47:20
180            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
180-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:42:13-118
181            android:exported="false" >
181-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:43:13-37
182            <intent-filter>
182-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:44:13-46:29
183                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
183-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:45:17-103
183-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:45:25-100
184            </intent-filter>
185        </receiver>
186
187        <provider
187-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
188            android:name="androidx.startup.InitializationProvider"
188-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
189            android:authorities="com.usefulteam.easy_social_share_example.androidx-startup"
189-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
190            android:exported="false" >
190-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
191            <meta-data
191-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
192                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
192-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
193                android:value="androidx.startup" />
193-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
194            <meta-data
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
195                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
196                android:value="androidx.startup" />
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
197        </provider>
198
199        <uses-library
199-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
200            android:name="androidx.window.extensions"
200-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
201            android:required="false" />
201-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
202        <uses-library
202-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
203            android:name="androidx.window.sidecar"
203-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
204            android:required="false" />
204-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
205
206        <receiver
206-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
207            android:name="androidx.profileinstaller.ProfileInstallReceiver"
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
208            android:directBootAware="false"
208-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
209            android:enabled="true"
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
210            android:exported="true"
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
211            android:permission="android.permission.DUMP" >
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
212            <intent-filter>
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
213                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
213-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
213-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
214            </intent-filter>
215            <intent-filter>
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
216                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
217            </intent-filter>
218            <intent-filter>
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
219                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
220            </intent-filter>
221            <intent-filter>
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
222                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
223            </intent-filter>
224        </receiver>
225    </application>
226
227</manifest>
