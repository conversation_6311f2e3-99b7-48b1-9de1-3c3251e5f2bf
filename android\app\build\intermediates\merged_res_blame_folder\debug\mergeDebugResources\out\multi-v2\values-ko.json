{"logs": [{"outputFile": "com.usefulteam.easy_social_share_example.app-mergeDebugResources-33:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\09538e6dbf47d754d340c6096b073b9b\\transformed\\jetified-facebook-common-17.0.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,310,429,538,618,702,777,862,948,1055,1157,1245,1333,1428,1541,1620,1699,1828,1922,2023,2129,2225", "endColumns": "134,119,118,108,79,83,74,84,85,106,101,87,87,94,112,78,78,128,93,100,105,95,101", "endOffsets": "185,305,424,533,613,697,772,857,943,1050,1152,1240,1328,1423,1536,1615,1694,1823,1917,2018,2124,2220,2322"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3362,3497,3617,3736,3845,3925,4009,4084,4169,4255,4362,4464,4552,4640,4735,4848,4927,5006,5135,5229,5330,5436,5532", "endColumns": "134,119,118,108,79,83,74,84,85,106,101,87,87,94,112,78,78,128,93,100,105,95,101", "endOffsets": "3492,3612,3731,3840,3920,4004,4079,4164,4250,4357,4459,4547,4635,4730,4843,4922,5001,5130,5224,5325,5431,5527,5629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f958ee96b464852d797ff4a06c0b43c\\transformed\\core-1.15.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "29,30,31,32,33,34,35,60", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2685,2777,2877,2971,3068,3164,3262,5712", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "2772,2872,2966,3063,3159,3257,3357,5808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,839,930,1022,1117,1211,1312,1405,1500,1594,1685,1776,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,75,90,91,94,93,100,92,94,93,90,90,78,97,93,94,99,96,99,151,93,77", "endOffsets": "197,291,392,474,572,678,758,834,925,1017,1112,1206,1307,1400,1495,1589,1680,1771,1850,1948,2042,2137,2237,2334,2434,2586,2680,2758"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,839,930,1022,1117,1211,1312,1405,1500,1594,1685,1776,1855,1953,2047,2142,2242,2339,2439,2591,5634", "endColumns": "96,93,100,81,97,105,79,75,90,91,94,93,100,92,94,93,90,90,78,97,93,94,99,96,99,151,93,77", "endOffsets": "197,291,392,474,572,678,758,834,925,1017,1112,1206,1307,1400,1495,1589,1680,1771,1850,1948,2042,2137,2237,2334,2434,2586,2680,5707"}}]}]}