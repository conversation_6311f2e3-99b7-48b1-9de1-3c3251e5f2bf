{"logs": [{"outputFile": "com.usefulteam.easy_social_share_example.app-mergeDebugResources-33:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\197f12b192a3f06912c946d4cbd2dd7d\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,6,12,20,31,43,49,55,56,57,58,59,331,2105,2111,3196,3204,3219", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,377,550,769,1142,1456,1644,1831,1884,1944,1996,2041,20313,136161,136356,174030,174312,174926", "endLines": "2,11,19,27,42,48,54,55,56,57,58,59,331,2110,2115,3203,3218,3234", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "204,545,764,983,1451,1639,1826,1879,1939,1991,2036,2075,20368,136351,136509,174307,174921,175575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\861e783eb94c5cf895f7fc30dea4170d\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "133,280,281,282,283,1798,1800,1801,1806,1808", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "7101,17320,17373,17426,17479,117611,117787,117909,118171,118366", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "7185,17368,17421,17474,17527,117672,117904,117965,118232,118428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c8746a36ac065afed39d95b2852a559\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "332,339,362,2884,2889", "startColumns": "4,4,4,4,4", "startOffsets": "20373,20672,21881,164396,164566", "endLines": "332,339,362,2888,2892", "endColumns": "56,64,63,24,24", "endOffsets": "20425,20732,21940,164561,164710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1ea03ff4645f48d24e3fb5a42281310d\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "3,90,91,92,93,234,235,236,454,1507,1509,1512,2654", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "209,4245,4306,4368,4430,14051,14110,14167,29027,95706,95770,95896,154028", "endLines": "3,90,91,92,93,234,235,236,460,1508,1511,1514,2681", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "256,4301,4363,4425,4489,14105,14162,14216,29436,95765,95891,96019,154947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e60d7cc8f585e105683d15c0883739b4\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "338,359", "startColumns": "4,4", "startOffsets": "20630,21717", "endColumns": "41,59", "endOffsets": "20667,21772"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4f958ee96b464852d797ff4a06c0b43c\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "30,70,71,88,89,131,132,249,250,251,252,253,254,255,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,334,335,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,367,396,397,398,399,400,401,402,427,1793,1794,1799,1802,1807,1951,1952,2648,2682,2828,2863,2893,2926", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1082,2745,2817,4114,4179,6969,7038,15070,15140,15208,15280,15350,15411,15485,16342,16403,16464,16526,16590,16652,16713,16781,16881,16941,17007,17080,17149,17206,17258,17985,18057,18133,18198,18257,18316,18376,18436,18496,18556,18616,18676,18736,18796,18856,18916,18975,19035,19095,19155,19215,19275,19335,19395,19455,19515,19575,19634,19694,19754,19813,19872,19931,19990,20049,20464,20499,20783,20838,20901,20956,21014,21072,21133,21196,21253,21304,21354,21415,21472,21538,21572,21607,22188,24207,24274,24346,24415,24484,24558,24630,27063,117293,117410,117677,117970,118237,129676,129748,153825,154952,161909,163715,164715,165397", "endLines": "30,70,71,88,89,131,132,249,250,251,252,253,254,255,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,334,335,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,367,396,397,398,399,400,401,402,427,1793,1797,1799,1805,1807,1951,1952,2653,2691,2862,2883,2925,2931", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1137,2812,2900,4174,4240,7033,7096,15135,15203,15275,15345,15406,15480,15553,16398,16459,16521,16585,16647,16708,16776,16876,16936,17002,17075,17144,17201,17253,17315,18052,18128,18193,18252,18311,18371,18431,18491,18551,18611,18671,18731,18791,18851,18911,18970,19030,19090,19150,19210,19270,19330,19390,19450,19510,19570,19629,19689,19749,19808,19867,19926,19985,20044,20103,20494,20529,20833,20896,20951,21009,21067,21128,21191,21248,21299,21349,21410,21467,21533,21567,21602,21637,22253,24269,24341,24410,24479,24553,24625,24713,27129,117405,117606,117782,118166,118361,129743,129810,154023,155248,163710,164391,165392,165559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e93556932885008eff7df21847fbdad2\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2126,2142,2148,3235,3251", "startColumns": "4,4,4,4,4", "startOffsets": "137031,137456,137634,175580,175991", "endLines": "2141,2147,2157,3250,3254", "endColumns": "24,24,24,24,24", "endOffsets": "137451,137629,137913,175986,176113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f734b899c9b5bcf473e5c8a79b68b93\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "360", "startColumns": "4", "startOffsets": "21777", "endColumns": "53", "endOffsets": "21826"}}, {"source": "D:\\flutter-packages\\easy_social_share\\example\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1515,1519", "startColumns": "4,4", "startOffsets": "96024,96205", "endLines": "1518,1521", "endColumns": "12,12", "endOffsets": "96200,96369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d4ac97fa1625eb25f0f50dfca8b23d95\\transformed\\jetified-lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "361", "startColumns": "4", "startOffsets": "21831", "endColumns": "49", "endOffsets": "21876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "5,28,29,60,61,62,63,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,256,257,258,259,260,261,262,263,264,284,285,286,287,288,289,290,291,327,328,329,330,333,336,337,340,357,363,364,365,366,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,426,428,429,430,431,432,433,441,442,446,450,461,466,472,479,483,487,492,496,500,504,508,512,516,522,526,532,536,542,546,551,555,558,562,568,572,578,582,588,591,595,599,603,607,611,612,613,614,617,620,623,626,630,631,632,633,634,637,639,641,643,648,649,653,659,663,664,666,677,678,682,688,692,693,694,698,725,729,730,734,762,932,958,1129,1155,1186,1194,1200,1214,1236,1241,1246,1256,1265,1274,1278,1285,1293,1300,1301,1310,1313,1316,1320,1324,1328,1331,1332,1337,1342,1352,1357,1364,1370,1371,1374,1378,1383,1385,1387,1390,1393,1395,1399,1402,1409,1412,1415,1419,1421,1425,1427,1429,1431,1435,1443,1451,1463,1469,1478,1481,1492,1495,1496,1501,1502,1522,1591,1661,1662,1672,1681,1682,1684,1688,1691,1694,1697,1700,1703,1706,1709,1713,1716,1719,1722,1726,1729,1733,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1759,1761,1762,1763,1764,1765,1766,1767,1768,1770,1771,1773,1774,1776,1778,1779,1781,1782,1783,1784,1785,1786,1788,1789,1790,1791,1792,1809,1811,1813,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1829,1830,1831,1832,1833,1834,1836,1840,1844,1845,1846,1847,1848,1849,1853,1854,1855,1856,1858,1860,1862,1864,1866,1867,1868,1869,1871,1873,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1889,1890,1891,1892,1894,1896,1897,1899,1900,1902,1904,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1919,1920,1921,1922,1924,1925,1926,1927,1928,1930,1932,1934,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,2001,2076,2079,2082,2085,2099,2116,2158,2187,2214,2223,2285,2644,2692,2810,2932,2956,2962,2968,2989,3113,3133,3139,3143,3149,3184,3255,3321,3341,3396,3408,3434", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,988,1033,2080,2121,2176,2235,2297,2378,2439,2514,2590,2667,2905,2990,3072,3148,3224,3301,3379,3485,3591,3670,3999,4056,5616,5690,5765,5830,5896,5956,6017,6089,6162,6229,6297,6356,6415,6474,6533,6592,6646,6700,6753,6807,6861,6915,7190,7264,7343,7416,7490,7561,7633,7705,7778,7835,7893,7966,8040,8114,8189,8261,8334,8404,8475,8535,8596,8665,8734,8804,8878,8954,9018,9095,9171,9248,9313,9382,9459,9534,9603,9671,9748,9814,9875,9972,10037,10106,10205,10276,10335,10393,10450,10509,10573,10644,10716,10788,10860,10932,10999,11067,11135,11194,11257,11321,11411,11502,11562,11628,11695,11761,11831,11895,11948,12015,12076,12143,12256,12314,12377,12442,12507,12582,12655,12727,12776,12837,12898,12959,13021,13085,13149,13213,13278,13341,13401,13462,13528,13587,13647,13709,13780,13840,15558,15644,15731,15821,15908,15996,16078,16161,16251,17532,17584,17642,17687,17753,17817,17874,17931,20108,20165,20213,20262,20430,20534,20581,20737,21642,21945,22009,22071,22131,22258,22332,22402,22480,22534,22604,22689,22737,22783,22844,22907,22973,23037,23108,23171,23236,23300,23361,23422,23474,23547,23621,23690,23765,23839,23913,24054,27010,27134,27212,27302,27390,27486,27576,28158,28247,28494,28775,29441,29726,30119,30596,30818,31040,31316,31543,31773,32003,32233,32463,32690,33109,33335,33760,33990,34418,34637,34920,35128,35259,35486,35912,36137,36564,36785,37210,37330,37606,37907,38231,38522,38836,38973,39104,39209,39451,39618,39822,40030,40301,40413,40525,40630,40747,40961,41107,41247,41333,41681,41769,42015,42433,42682,42764,42862,43454,43554,43806,44230,44485,44579,44668,44905,46929,47171,47273,47526,49682,60214,61730,72361,73889,75646,76272,76692,77753,79018,79274,79510,80057,80551,81156,81354,81934,82498,82873,82991,83529,83686,83882,84155,84411,84581,84722,84786,85151,85518,86194,86458,86796,87149,87243,87429,87735,87997,88122,88249,88488,88699,88818,89011,89188,89643,89824,89946,90205,90318,90505,90607,90714,90843,91118,91626,92122,92999,93293,93863,94012,94744,94916,95000,95336,95428,96374,101620,107009,107071,107649,108233,108324,108437,108666,108826,108978,109149,109315,109484,109651,109814,110057,110227,110400,110571,110845,111044,111249,111579,111663,111759,111855,111953,112053,112155,112257,112359,112461,112563,112663,112759,112871,113000,113123,113254,113385,113483,113597,113691,113831,113965,114061,114173,114273,114389,114485,114597,114697,114837,114973,115137,115267,115425,115575,115716,115860,115995,116107,116257,116385,116513,116649,116781,116911,117041,117153,118433,118579,118723,118861,118927,119017,119093,119197,119287,119389,119497,119605,119705,119785,119877,119975,120085,120163,120269,120361,120465,120575,120697,120860,121017,121097,121197,121287,121397,121487,121728,121822,121928,122020,122120,122232,122346,122462,122578,122672,122786,122898,123000,123120,123242,123324,123428,123548,123674,123772,123866,123954,124066,124182,124304,124416,124591,124707,124793,124885,124997,125121,125188,125314,125382,125510,125654,125782,125851,125946,126061,126174,126273,126382,126493,126604,126705,126810,126910,127040,127131,127254,127348,127460,127546,127650,127746,127834,127952,128056,128160,128286,128374,128482,128582,128672,128782,128866,128968,129052,129106,129170,129276,129362,129472,129556,132638,135254,135372,135487,135567,135928,136514,137918,139262,140623,141011,143786,153690,155253,161337,165564,166315,166577,166777,167156,171434,172040,172269,172420,172635,173718,176118,179144,179888,182019,182359,183670", "endLines": "5,28,29,60,61,62,63,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,256,257,258,259,260,261,262,263,264,284,285,286,287,288,289,290,291,327,328,329,330,333,336,337,340,357,363,364,365,366,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,426,428,429,430,431,432,440,441,445,449,453,465,471,478,482,486,491,495,499,503,507,511,515,521,525,531,535,541,545,550,554,557,561,567,571,577,581,587,590,594,598,602,606,610,611,612,613,616,619,622,625,629,630,631,632,633,636,638,640,642,647,648,652,658,662,663,665,676,677,681,687,691,692,693,697,724,728,729,733,761,931,957,1128,1154,1185,1193,1199,1213,1235,1240,1245,1255,1264,1273,1277,1284,1292,1299,1300,1309,1312,1315,1319,1323,1327,1330,1331,1336,1341,1351,1356,1363,1369,1370,1373,1377,1382,1384,1386,1389,1392,1394,1398,1401,1408,1411,1414,1418,1420,1424,1426,1428,1430,1434,1442,1450,1462,1468,1477,1480,1491,1494,1495,1500,1501,1506,1590,1660,1661,1671,1680,1681,1683,1687,1690,1693,1696,1699,1702,1705,1708,1712,1715,1718,1721,1725,1728,1732,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1758,1760,1761,1762,1763,1764,1765,1766,1767,1769,1770,1772,1773,1775,1777,1778,1780,1781,1782,1783,1784,1785,1787,1788,1789,1790,1791,1792,1810,1812,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1828,1829,1830,1831,1832,1833,1835,1839,1843,1844,1845,1846,1847,1848,1852,1853,1854,1855,1857,1859,1861,1863,1865,1866,1867,1868,1870,1872,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1888,1889,1890,1891,1893,1895,1896,1898,1899,1901,1903,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1918,1919,1920,1921,1923,1924,1925,1926,1927,1929,1931,1933,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,2075,2078,2081,2084,2098,2104,2125,2186,2213,2222,2284,2643,2647,2719,2827,2955,2961,2967,2988,3112,3132,3138,3142,3148,3183,3195,3320,3340,3395,3407,3433,3440", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "372,1028,1077,2116,2171,2230,2292,2373,2434,2509,2585,2662,2740,2985,3067,3143,3219,3296,3374,3480,3586,3665,3745,4051,4109,5685,5760,5825,5891,5951,6012,6084,6157,6224,6292,6351,6410,6469,6528,6587,6641,6695,6748,6802,6856,6910,6964,7259,7338,7411,7485,7556,7628,7700,7773,7830,7888,7961,8035,8109,8184,8256,8329,8399,8470,8530,8591,8660,8729,8799,8873,8949,9013,9090,9166,9243,9308,9377,9454,9529,9598,9666,9743,9809,9870,9967,10032,10101,10200,10271,10330,10388,10445,10504,10568,10639,10711,10783,10855,10927,10994,11062,11130,11189,11252,11316,11406,11497,11557,11623,11690,11756,11826,11890,11943,12010,12071,12138,12251,12309,12372,12437,12502,12577,12650,12722,12771,12832,12893,12954,13016,13080,13144,13208,13273,13336,13396,13457,13523,13582,13642,13704,13775,13835,13903,15639,15726,15816,15903,15991,16073,16156,16246,16337,17579,17637,17682,17748,17812,17869,17926,17980,20160,20208,20257,20308,20459,20576,20625,20778,21669,22004,22066,22126,22183,22327,22397,22475,22529,22599,22684,22732,22778,22839,22902,22968,23032,23103,23166,23231,23295,23356,23417,23469,23542,23616,23685,23760,23834,23908,24049,24119,27058,27207,27297,27385,27481,27571,28153,28242,28489,28770,29022,29721,30114,30591,30813,31035,31311,31538,31768,31998,32228,32458,32685,33104,33330,33755,33985,34413,34632,34915,35123,35254,35481,35907,36132,36559,36780,37205,37325,37601,37902,38226,38517,38831,38968,39099,39204,39446,39613,39817,40025,40296,40408,40520,40625,40742,40956,41102,41242,41328,41676,41764,42010,42428,42677,42759,42857,43449,43549,43801,44225,44480,44574,44663,44900,46924,47166,47268,47521,49677,60209,61725,72356,73884,75641,76267,76687,77748,79013,79269,79505,80052,80546,81151,81349,81929,82493,82868,82986,83524,83681,83877,84150,84406,84576,84717,84781,85146,85513,86189,86453,86791,87144,87238,87424,87730,87992,88117,88244,88483,88694,88813,89006,89183,89638,89819,89941,90200,90313,90500,90602,90709,90838,91113,91621,92117,92994,93288,93858,94007,94739,94911,94995,95331,95423,95701,101615,107004,107066,107644,108228,108319,108432,108661,108821,108973,109144,109310,109479,109646,109809,110052,110222,110395,110566,110840,111039,111244,111574,111658,111754,111850,111948,112048,112150,112252,112354,112456,112558,112658,112754,112866,112995,113118,113249,113380,113478,113592,113686,113826,113960,114056,114168,114268,114384,114480,114592,114692,114832,114968,115132,115262,115420,115570,115711,115855,115990,116102,116252,116380,116508,116644,116776,116906,117036,117148,117288,118574,118718,118856,118922,119012,119088,119192,119282,119384,119492,119600,119700,119780,119872,119970,120080,120158,120264,120356,120460,120570,120692,120855,121012,121092,121192,121282,121392,121482,121723,121817,121923,122015,122115,122227,122341,122457,122573,122667,122781,122893,122995,123115,123237,123319,123423,123543,123669,123767,123861,123949,124061,124177,124299,124411,124586,124702,124788,124880,124992,125116,125183,125309,125377,125505,125649,125777,125846,125941,126056,126169,126268,126377,126488,126599,126700,126805,126905,127035,127126,127249,127343,127455,127541,127645,127741,127829,127947,128051,128155,128281,128369,128477,128577,128667,128777,128861,128963,129047,129101,129165,129271,129357,129467,129551,129671,135249,135367,135482,135562,135923,136156,137026,139257,140618,141006,143781,153685,153820,156605,161904,166310,166572,166772,167151,171429,172035,172264,172415,172630,173713,174025,179139,179883,182014,182354,183665,183868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7feabc10bdb4aab53c74638db0e2916f\\transformed\\jetified-facebook-share-17.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,11", "startColumns": "4,4,4,4,4", "startOffsets": "55,158,242,304,740", "endLines": "2,3,4,10,13", "endColumns": "102,83,61,12,12", "endOffsets": "153,237,299,735,902"}, "to": {"startLines": "98,99,104,1992,1998", "startColumns": "4,4,4,4,4", "startOffsets": "4793,4896,5263,132035,132471", "endLines": "98,99,104,1997,2000", "endColumns": "102,83,61,12,12", "endOffsets": "4891,4975,5320,132466,132633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ad8082b815ad637a27bc1f7391b9211e\\transformed\\jetified-lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "358", "startColumns": "4", "startOffsets": "21674", "endColumns": "42", "endOffsets": "21712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\16555e5d675161666021eacdd684a3d2\\transformed\\browser-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375", "endColumns": "57,65,62,61,70,71", "endOffsets": "108,174,237,299,370,442"}, "to": {"startLines": "82,83,84,85,232,233", "startColumns": "4,4,4,4,4,4", "startOffsets": "3750,3808,3874,3937,13908,13979", "endColumns": "57,65,62,61,70,71", "endOffsets": "3803,3869,3932,3994,13974,14046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\09538e6dbf47d754d340c6096b073b9b\\transformed\\jetified-facebook-common-17.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,54,60,71,82,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,202,290,370,449,513,590,665,732,814,895,968,1023,1092,1171,1235,1309,1382,1455,1527,1600,1673,1738,1807,1872,2009,2124,2242,2337,2402,2470,2528,2600,2672,2769,2860,2934,3008,3121,3222,3285,3505,3670,3747,3829,3954,4040,4164,4458,4808,5429,6060,6239", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,53,59,70,81,84,112", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,12,12,12,12,12,24", "endOffsets": "197,285,365,444,508,585,660,727,809,890,963,1018,1087,1166,1230,1304,1377,1450,1522,1595,1668,1733,1802,1867,2004,2119,2237,2332,2397,2465,2523,2595,2667,2764,2855,2929,3003,3116,3217,3280,3500,3665,3742,3824,3949,4035,4159,4453,4803,5424,6055,6234,7468"}, "to": {"startLines": "94,95,96,97,100,101,102,103,105,106,107,108,237,238,239,240,241,242,243,244,245,246,247,248,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,1956,1961,1967,1978,1989,3441", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4494,4546,4634,4714,4980,5044,5121,5196,5325,5407,5488,5561,14221,14290,14369,14433,14507,14580,14653,14725,14798,14871,14936,15005,24718,24855,24970,25088,25183,25248,25316,25374,25446,25518,25615,25706,25780,25854,25967,26068,26131,26351,26516,26593,26675,26800,26886,129960,130254,130604,131225,131856,183873", "endLines": "94,95,96,97,100,101,102,103,105,106,107,108,237,238,239,240,241,242,243,244,245,246,247,248,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,1960,1966,1977,1988,1991,3468", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,12,12,12,12,12,24", "endOffsets": "4541,4629,4709,4788,5039,5116,5191,5258,5402,5483,5556,5611,14285,14364,14428,14502,14575,14648,14720,14793,14866,14931,15000,15065,24850,24965,25083,25178,25243,25311,25369,25441,25513,25610,25701,25775,25849,25962,26063,26126,26346,26511,26588,26670,26795,26881,27005,130249,130599,131220,131851,132030,185102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\85879f220671a879b538e8ef16ed1744\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "395", "startColumns": "4", "startOffsets": "24124", "endColumns": "82", "endOffsets": "24202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28f988f0d4c2cc22199e4c3cefdd595e\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "4,1953,2720,2726", "startColumns": "4,4,4,4", "startOffsets": "261,129815,156610,156821", "endLines": "4,1955,2725,2809", "endColumns": "60,12,24,24", "endOffsets": "317,129955,156816,161332"}}]}]}