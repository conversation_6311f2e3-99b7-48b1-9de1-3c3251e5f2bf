-- Merging decision tree log ---
application
INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:4:5-46:19
INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml
MERGED from [com.facebook.android:facebook-share:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7feabc10bdb4aab53c74638db0e2916f\transformed\jetified-facebook-share-17.0.0\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-share:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7feabc10bdb4aab53c74638db0e2916f\transformed\jetified-facebook-share-17.0.0\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:21:5-42:19
MERGED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:21:5-42:19
MERGED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:18:5-48:19
MERGED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:18:5-48:19
MERGED from [com.facebook.android:facebook-bolts:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6616919f02e588b43df254daacf43322\transformed\jetified-facebook-bolts-17.0.0\AndroidManifest.xml:16:5-17:19
MERGED from [com.facebook.android:facebook-bolts:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6616919f02e588b43df254daacf43322\transformed\jetified-facebook-bolts-17.0.0\AndroidManifest.xml:16:5-17:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\358dd9479c84a3ed01b3c101674f7c01\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\358dd9479c84a3ed01b3c101674f7c01\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
	android:extractNativeLibs
		INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from [com.facebook.android:facebook-share:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7feabc10bdb4aab53c74638db0e2916f\transformed\jetified-facebook-share-17.0.0\AndroidManifest.xml:16:18-44
	android:name
		INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml
provider#androidx.core.content.FileProvider
INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:37:9-45:20
	android:authorities
		INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:1:1-94:12
MERGED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:1:1-94:12
INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.2.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:easy_social_share] D:\flutter-packages\easy_social_share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.android:facebook-share:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7feabc10bdb4aab53c74638db0e2916f\transformed\jetified-facebook-share-17.0.0\AndroidManifest.xml:9:1-19:12
MERGED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:9:1-44:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d0ad2c9a7eee0ad2b557032bddebd70\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f96942f3d58d17244242eba2c1bcf26c\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:9:1-50:12
MERGED from [com.facebook.android:facebook-bolts:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6616919f02e588b43df254daacf43322\transformed\jetified-facebook-bolts-17.0.0\AndroidManifest.xml:9:1-19:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e60d7cc8f585e105683d15c0883739b4\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16555e5d675161666021eacdd684a3d2\transformed\browser-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df95e149204d983cb4c11efbc84ab4c1\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\e87333264a09c4cac3f29233d2781c47\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\da12e2157864b6098a4aed4f7e77e271\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4ac97fa1625eb25f0f50dfca8b23d95\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\54c71f8b86af50ae2bc2eead2539f2dd\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b78e5e5d04fd1ce00b64c8de20f1ba5\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad8082b815ad637a27bc1f7391b9211e\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\511957125cc1b906a05dee6838238bf7\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc77f214097347dd090a046ace804f34\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e93556932885008eff7df21847fbdad2\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2478386b28aa149978ce84e3a3e3f34c\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a0f53fd92f6561a1dd451c29da9990\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\861e783eb94c5cf895f7fc30dea4170d\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1ea03ff4645f48d24e3fb5a42281310d\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb263d6c3807b4994a64a61fe4ea2bcf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\358dd9479c84a3ed01b3c101674f7c01\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:2:1-13:12
	package
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:3:5-55
		INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:2:5-51
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:52:5-57:15
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.2.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.2.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:17:5-19:15
MERGED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:17:5-19:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:53:9-56:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:54:13-73
	android:name
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:54:21-70
data
ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:55:13-51
	android:mimeType
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:55:19-48
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:79:5-76
	android:name
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:79:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:83:5-75
	android:name
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:83:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:87:5-75
	android:name
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:87:22-72
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:89:5-80
	android:name
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:89:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:91:5-92:38
	android:maxSdkVersion
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:92:9-35
	android:name
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:91:22-78
uses-permission#android.permission.INTERNET
ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:93:5-67
MERGED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:93:5-67
MERGED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:93:5-67
	android:name
		ADDED from D:\flutter-packages\easy_social_share\example\android\app\src\main\AndroidManifest.xml:93:22-64
uses-sdk
INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.2.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.2.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:easy_social_share] D:\flutter-packages\easy_social_share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:easy_social_share] D:\flutter-packages\easy_social_share\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-share:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7feabc10bdb4aab53c74638db0e2916f\transformed\jetified-facebook-share-17.0.0\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-share:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7feabc10bdb4aab53c74638db0e2916f\transformed\jetified-facebook-share-17.0.0\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:13:5-15:41
MERGED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:13:5-15:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d0ad2c9a7eee0ad2b557032bddebd70\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d0ad2c9a7eee0ad2b557032bddebd70\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f96942f3d58d17244242eba2c1bcf26c\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f96942f3d58d17244242eba2c1bcf26c\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-bolts:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6616919f02e588b43df254daacf43322\transformed\jetified-facebook-bolts-17.0.0\AndroidManifest.xml:12:5-14:41
MERGED from [com.facebook.android:facebook-bolts:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6616919f02e588b43df254daacf43322\transformed\jetified-facebook-bolts-17.0.0\AndroidManifest.xml:12:5-14:41
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e60d7cc8f585e105683d15c0883739b4\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e60d7cc8f585e105683d15c0883739b4\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16555e5d675161666021eacdd684a3d2\transformed\browser-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16555e5d675161666021eacdd684a3d2\transformed\browser-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df95e149204d983cb4c11efbc84ab4c1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df95e149204d983cb4c11efbc84ab4c1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\e87333264a09c4cac3f29233d2781c47\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\e87333264a09c4cac3f29233d2781c47\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\da12e2157864b6098a4aed4f7e77e271\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\da12e2157864b6098a4aed4f7e77e271\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4ac97fa1625eb25f0f50dfca8b23d95\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\d4ac97fa1625eb25f0f50dfca8b23d95\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\54c71f8b86af50ae2bc2eead2539f2dd\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\54c71f8b86af50ae2bc2eead2539f2dd\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b78e5e5d04fd1ce00b64c8de20f1ba5\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b78e5e5d04fd1ce00b64c8de20f1ba5\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad8082b815ad637a27bc1f7391b9211e\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad8082b815ad637a27bc1f7391b9211e\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\511957125cc1b906a05dee6838238bf7\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\511957125cc1b906a05dee6838238bf7\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc77f214097347dd090a046ace804f34\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc77f214097347dd090a046ace804f34\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e93556932885008eff7df21847fbdad2\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e93556932885008eff7df21847fbdad2\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2478386b28aa149978ce84e3a3e3f34c\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2478386b28aa149978ce84e3a3e3f34c\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a0f53fd92f6561a1dd451c29da9990\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a0f53fd92f6561a1dd451c29da9990\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\861e783eb94c5cf895f7fc30dea4170d\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\861e783eb94c5cf895f7fc30dea4170d\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1ea03ff4645f48d24e3fb5a42281310d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1ea03ff4645f48d24e3fb5a42281310d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb263d6c3807b4994a64a61fe4ea2bcf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb263d6c3807b4994a64a61fe4ea2bcf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\358dd9479c84a3ed01b3c101674f7c01\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\358dd9479c84a3ed01b3c101674f7c01\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\flutter-packages\easy_social_share\example\android\app\src\debug\AndroidManifest.xml
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.2.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.2.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
	android:name
		ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-10.2.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
package#com.facebook.katana
ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:18:9-55
	android:name
		ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:18:18-52
activity#com.facebook.FacebookActivity
ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:22:9-25:66
	android:configChanges
		ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:24:13-96
	android:theme
		ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:25:13-63
	android:name
		ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:23:13-57
activity#com.facebook.CustomTabMainActivity
ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:26:9-71
	android:name
		ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:26:19-68
activity#com.facebook.CustomTabActivity
ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:27:9-41:20
	tools:node
		ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:30:13-31
	android:exported
		ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:29:13-36
	android:name
		ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:28:13-58
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.${applicationId}+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:31:13-40:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.com.usefulteam.easy_social_share_example+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:31:13-40:29
action#android.intent.action.VIEW
ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:32:17-69
	android:name
		ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:32:25-66
category#android.intent.category.DEFAULT
ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:34:17-76
	android:name
		ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:34:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:35:17-78
	android:name
		ADDED from [com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09538e6dbf47d754d340c6096b073b9b\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:35:27-75
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:16:5-79
	android:name
		ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:16:22-76
provider#com.facebook.internal.FacebookInitProvider
ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:29:9-32:40
	android:authorities
		ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:31:13-72
	android:exported
		ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:30:13-70
receiver#com.facebook.CurrentAccessTokenExpirationBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:34:9-40:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:36:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:35:13-86
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:37:13-39:29
action#com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:38:17-95
	android:name
		ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:38:25-92
receiver#com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:41:9-47:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:43:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:42:13-118
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:44:13-46:29
action#com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:45:17-103
	android:name
		ADDED from [com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6736a2071b653d6fee4505a94145cde\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:45:25-100
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad96eb741a92645dddd8dccfd6825e55\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.usefulteam.easy_social_share_example.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.usefulteam.easy_social_share_example.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f958ee96b464852d797ff4a06c0b43c\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\358dd9479c84a3ed01b3c101674f7c01\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\358dd9479c84a3ed01b3c101674f7c01\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
