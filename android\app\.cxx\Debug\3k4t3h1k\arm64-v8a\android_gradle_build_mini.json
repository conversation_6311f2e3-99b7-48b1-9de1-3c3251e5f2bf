{"buildFiles": ["C:\\Users\\<USER>\\fvm\\versions\\stable\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter-packages\\easy_social_share\\example\\android\\app\\.cxx\\Debug\\3k4t3h1k\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutter-packages\\easy_social_share\\example\\android\\app\\.cxx\\Debug\\3k4t3h1k\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}